'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import {
  FolderOpen,
  File,
  FileText,
  Download,
  Trash2,
  Eye,
  MoreHorizontal,
  Search,
  Filter,
  Upload,
  Calendar,
  FileSpreadsheet,
  Database,
  ArrowUpDown,
  Grid3X3,
  List,
  Plus,
  Settings
} from 'lucide-react'
import Link from 'next/link'
import { useAuth } from '@/components/auth/auth-provider'
import { getUserDataSources, dataSourcesService } from '@/lib/firestore'
import { useToast } from '@/components/ui/toast'
import { useRouter } from 'next/navigation'
import { useConfirmation } from '@/components/ui/confirmation-modal'

interface FileItem {
  id: string
  name: string
  type: 'csv' | 'xlsx' | 'json' | 'firebase_sync' | 'google_sheets'
  size: string
  uploadDate: string
  status: 'processed' | 'processing' | 'error'
  records: number
  lastModified: string
  sourceType?: 'file_upload' | 'firebase_connection' | 'google_sheets'
  connectionId?: string
}

export default function FilesPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')

  const [files, setFiles] = useState<FileItem[]>([])
  const [isLoadingFiles, setIsLoadingFiles] = useState(false)
  const [filesError, setFilesError] = useState<string>('')

  const { user } = useAuth()
  const { toast } = useToast()
  const router = useRouter()
  const { confirm, ConfirmationModal } = useConfirmation()

  // Load user's data sources (files) from Firebase
  useEffect(() => {
    const loadFiles = async () => {
      if (!user) return

      try {
        setIsLoadingFiles(true)
        setFilesError('')

        const firebaseDataSources = await getUserDataSources(user.uid)

        // Transform Firebase data sources to match our FileItem interface
        const transformedFiles: FileItem[] = firebaseDataSources.map((source: any) => {
          // Calculate time since last update
          const updatedAt = new Date(source.updated_at)
          const now = new Date()
          const diffInHours = Math.floor((now.getTime() - updatedAt.getTime()) / (1000 * 60 * 60))

          let lastModified: string
          if (diffInHours < 1) {
            lastModified = 'Just now'
          } else if (diffInHours < 24) {
            lastModified = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
          } else {
            const diffInDays = Math.floor(diffInHours / 24)
            lastModified = `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
          }

          // Determine source type and connection info
          let sourceType: FileItem['sourceType'] = 'file_upload'
          let connectionId: string | undefined

          if (source.type === 'firebase_sync') {
            sourceType = 'firebase_connection'
            connectionId = source.metadata?.connection_id
          } else if (source.type === 'google_sheets') {
            sourceType = 'google_sheets'
          }

          return {
            id: source.id,
            name: source.name,
            type: source.type,
            size: source.metadata?.file_size || (source.type === 'firebase_sync' ? 'Synced Data' : 'Unknown'),
            uploadDate: source.created_at.split('T')[0], // Extract date part
            status: source.status === 'active' ? 'processed' : source.status === 'error' ? 'error' : 'processing',
            records: source.metadata?.record_count || source.config?.record_count || 0,
            lastModified,
            sourceType,
            connectionId
          }
        })

        setFiles(transformedFiles)
      } catch (error) {
        console.error('Failed to load files:', error)
        setFilesError('Failed to load your files. Please try again.')
      } finally {
        setIsLoadingFiles(false)
      }
    }

    loadFiles()
  }, [user])

  // Action handlers
  const handleCreateReport = (file: FileItem) => {
    // Store the selected data source in localStorage for the report creation page
    localStorage.setItem('selectedDataSource', JSON.stringify({
      id: file.id,
      name: file.name,
      type: file.type,
      sourceType: file.sourceType,
      connectionId: file.connectionId
    }))

    router.push('/dashboard/create-report')

    toast({
      type: 'info',
      title: 'Creating Report',
      description: `Redirecting to report builder with data from "${file.name}"`,
      duration: 3000
    })
  }

  const handleDownloadData = async (file: FileItem) => {
    try {
      toast({
        type: 'info',
        title: 'Preparing Download',
        description: `Exporting data from "${file.name}"...`,
        duration: 3000
      })

      // For Firebase connections, we'll export the synced data
      if (file.sourceType === 'firebase_connection') {
        // In a real implementation, this would fetch the actual synced data
        const exportData = {
          source: file.name,
          type: file.type,
          records: file.records,
          exportedAt: new Date().toISOString(),
          note: 'This is a sample export. In production, this would contain the actual synced Firebase data.'
        }

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${file.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_export.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toast({
          type: 'success',
          title: 'Download Complete',
          description: `Data from "${file.name}" has been exported successfully.`,
          duration: 4000
        })
      } else if (file.sourceType === 'google_sheets') {
        // For Google Sheets, fetch the actual data and export it
        const { googleSheetsService } = await import('@/lib/google-sheets')

        // Get the data source details to extract sheet info
        const dataSource = await dataSourcesService.getById(file.id)
        if (!dataSource) {
          throw new Error(`Data source not found for ID: ${file.id}`)
        }

        if (!dataSource.config) {
          throw new Error(`Google Sheets configuration not found for: ${file.name}`)
        }

        const { sheet_id, sheet_tab, sheet_name } = dataSource.config

        if (!sheet_id || !sheet_tab) {
          throw new Error(`Invalid Google Sheets configuration: missing sheet_id or sheet_tab`)
        }

        // Check if Google access is still valid before attempting to fetch data
        const hasValidAccess = await googleSheetsService.hasValidGoogleAccess()
        if (!hasValidAccess) {
          toast({
            type: 'warning',
            title: 'Google Authentication Expired',
            description: 'Your Google access has expired. Click "Re-authenticate" to sign in again.',
            duration: 8000,
            action: {
              label: 'Re-authenticate',
              onClick: () => router.push('/dashboard/data/google-sheets')
            }
          })
          return
        }

        // Fetch the actual sheet data
        const sheetData = await googleSheetsService.getSheetData(sheet_id, sheet_tab)

        // Create export data with both JSON and CSV formats
        const exportData = {
          source: file.name,
          type: file.type,
          sheet_info: {
            sheet_id,
            sheet_name,
            sheet_tab,
            url: dataSource.config.sheet_url
          },
          schema: sheetData.schema,
          data: sheetData.data,
          total_rows: sheetData.totalRows,
          exported_at: new Date().toISOString()
        }

        // Create CSV content for easier data analysis
        const headers = Object.keys(sheetData.schema)
        const csvContent = [
          headers.join(','),
          ...sheetData.data.map(row =>
            headers.map(header => {
              const value = row[header]
              // Escape commas and quotes in CSV
              if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                return `"${value.replace(/"/g, '""')}"`
              }
              return value || ''
            }).join(',')
          )
        ].join('\n')

        // Download JSON file
        const jsonBlob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
        const jsonUrl = URL.createObjectURL(jsonBlob)
        const jsonLink = document.createElement('a')
        jsonLink.href = jsonUrl
        jsonLink.download = `${file.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_export.json`
        document.body.appendChild(jsonLink)
        jsonLink.click()
        document.body.removeChild(jsonLink)
        URL.revokeObjectURL(jsonUrl)

        // Also download CSV file for easier data analysis
        const csvBlob = new Blob([csvContent], { type: 'text/csv' })
        const csvUrl = URL.createObjectURL(csvBlob)
        const csvLink = document.createElement('a')
        csvLink.href = csvUrl
        csvLink.download = `${file.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_data.csv`
        document.body.appendChild(csvLink)
        csvLink.click()
        document.body.removeChild(csvLink)
        URL.revokeObjectURL(csvUrl)

        toast({
          type: 'success',
          title: 'Download Complete',
          description: `Data from "${file.name}" has been exported as JSON and CSV files.`,
          duration: 4000
        })
      } else {
        toast({
          type: 'warning',
          title: 'Download Not Available',
          description: 'Download functionality is not available for this data source type.',
          duration: 4000
        })
      }
    } catch (error) {
      console.error('Download error:', error)

      // Check if this is a Google authentication error
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while preparing the download.'
      const isGoogleAuthError = errorMessage.includes('access token') ||
                               errorMessage.includes('expired') ||
                               errorMessage.includes('authentication') ||
                               errorMessage.includes('No access token available')

      if (isGoogleAuthError) {
        toast({
          type: 'warning',
          title: 'Google Authentication Required',
          description: 'Your Google access has expired. Click "Re-authenticate" to sign in again.',
          duration: 8000,
          action: {
            label: 'Re-authenticate',
            onClick: () => router.push('/dashboard/data/google-sheets')
          }
        })
      } else {
        toast({
          type: 'error',
          title: 'Download Failed',
          description: errorMessage,
          duration: 5000
        })
      }
    }
  }

  const handleDeleteFile = async (file: FileItem) => {
    const performDelete = async () => {
      try {
        await dataSourcesService.delete(file.id)

        // Reload files to reflect the deletion
        const updatedFiles = files.filter(f => f.id !== file.id)
        setFiles(updatedFiles)

        toast({
          type: 'success',
          title: 'File Deleted',
          description: `"${file.name}" has been removed from your files.`,
          duration: 4000
        })
      } catch (error) {
        toast({
          type: 'error',
          title: 'Delete Failed',
          description: 'An error occurred while deleting the file.',
          duration: 5000
        })
      }
    }

    // Show enhanced confirmation modal
    confirm({
      title: 'Delete Data Source',
      description: `Are you sure you want to delete "${file.name}"? This will permanently remove the data source and all associated configurations. This action cannot be undone.`,
      confirmText: 'Delete Forever',
      cancelText: 'Keep It',
      type: 'danger',
      onConfirm: performDelete
    })
  }

  const handleViewFile = (file: FileItem) => {
    if (file.sourceType === 'firebase_connection') {
      router.push('/dashboard/data/firebase-sync')
    } else if (file.sourceType === 'google_sheets') {
      router.push('/dashboard/data/google-sheets')
    } else {
      toast({
        type: 'info',
        title: 'File Preview',
        description: `Viewing details for "${file.name}"`,
        duration: 3000
      })
      // In a real implementation, this would open a preview modal
    }
  }

  const getFileIcon = (type: string, sourceType?: string) => {
    switch (type) {
      case 'csv':
        return <FileText className="h-8 w-8 text-green-600" />
      case 'xlsx':
        return <FileSpreadsheet className="h-8 w-8 text-blue-600" />
      case 'json':
        return <Database className="h-8 w-8 text-purple-600" />
      case 'firebase_sync':
        return <Database className="h-8 w-8 text-orange-600" />
      case 'google_sheets':
        return <FileSpreadsheet className="h-8 w-8 text-green-600" />
      default:
        return <File className="h-8 w-8 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'processed':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Processed</Badge>
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Processing</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Error</Badge>
      default:
        return null
    }
  }

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Files Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Manage Your Files,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Effortlessly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                View, organize, and manage all your uploaded data files in one centralized location
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/dashboard/import">
                  <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100 font-semibold px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 w-full sm:w-auto">
                    <Upload className="mr-2 h-5 w-5" />
                    Upload New File
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl">
                <FolderOpen className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">+3</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">{files.length}</p>
              <p className="text-sm text-gray-500">Total Files</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl">
                <Database className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full">+12K</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {files.reduce((sum, file) => sum + file.records, 0).toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">Total Records</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
                <FileText className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded-full">
                {Math.round((files.filter(f => f.status === 'processed').length / files.length) * 100)}%
              </span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {files.filter(f => f.status === 'processed').length}
              </p>
              <p className="text-sm text-gray-500">Processed</p>
            </div>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
            <div className="flex items-center justify-between mb-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <span className="text-xs font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded-full">Today</span>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {files.filter(f => f.uploadDate === '2024-01-15').length}
              </p>
              <p className="text-sm text-gray-500">Uploaded Today</p>
            </div>
          </div>
        </div>

        {/* File Management Section */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
          {/* Header with Search and Controls */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Your Files</h2>
                <p className="text-sm text-gray-600 mt-1">Manage and organize your uploaded data files</p>
              </div>
              
              <div className="flex items-center space-x-3">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search files..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                
                {/* View Mode Toggle */}
                <div className="flex items-center bg-gray-100 rounded-xl p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'grid' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                    }`}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'list' ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
                    }`}
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Files Content */}
          <div className="p-6">
            {isLoadingFiles ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <Database className="h-10 w-10 text-gray-400 animate-pulse" />
                </div>
                <p className="text-xl font-medium text-gray-700 mb-2">Loading your files...</p>
                <p className="text-sm text-gray-500">Please wait while we fetch your data</p>
              </div>
            ) : filesError ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-red-100 to-red-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <FolderOpen className="h-10 w-10 text-red-400" />
                </div>
                <p className="text-xl font-medium text-gray-700 mb-2">Error loading files</p>
                <p className="text-sm text-gray-500 mb-6">{filesError}</p>
                <Button
                  onClick={() => window.location.reload()}
                  className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                >
                  Try Again
                </Button>
              </div>
            ) : filteredFiles.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <FolderOpen className="h-10 w-10 text-gray-400" />
                </div>
                <p className="text-xl font-medium text-gray-700 mb-2">
                  {searchQuery ? 'No files found' : 'No files uploaded yet'}
                </p>
                <p className="text-sm text-gray-500 mb-6">
                  {searchQuery ? 'Try adjusting your search terms' : 'Upload your first file to get started'}
                </p>
                <Link href="/dashboard/import">
                  <Button className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                    <Plus className="mr-2 h-4 w-4" />
                    Upload File
                  </Button>
                </Link>
              </div>
            ) : (
              <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                {filteredFiles.map((file) => (
                  <div
                    key={file.id}
                    className={`group bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-all duration-300 cursor-pointer ${
                      viewMode === 'list' ? 'flex items-center justify-between' : ''
                    }`}
                  >
                    <div className={`flex items-center space-x-3 ${viewMode === 'list' ? 'flex-1' : 'mb-4'}`}>
                      {getFileIcon(file.type, file.sourceType)}
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{file.name}</p>
                        <p className="text-sm text-gray-500">
                          {file.size} • {file.records > 0 ? `${file.records.toLocaleString()} records` : 'No records'}
                        </p>
                        {file.sourceType === 'firebase_connection' && (
                          <p className="text-xs text-orange-600 font-medium">Firebase Sync</p>
                        )}
                        {file.sourceType === 'google_sheets' && (
                          <p className="text-xs text-green-600 font-medium">Google Sheets</p>
                        )}
                      </div>
                    </div>
                    
                    <div className={`flex items-center justify-between ${viewMode === 'list' ? 'space-x-4' : ''}`}>
                      <div className={viewMode === 'list' ? 'flex items-center space-x-4' : 'mb-3'}>
                        {getStatusBadge(file.status)}
                        {viewMode === 'list' && (
                          <span className="text-sm text-gray-500">{file.lastModified}</span>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {file.sourceType === 'firebase_connection' ? (
                          <Link href="/dashboard/data/firebase-sync">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="opacity-0 group-hover:opacity-100 transition-opacity text-orange-600 hover:text-orange-700"
                              title="Manage Firebase Connection"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </Link>
                        ) : (
                          <Button
                            size="sm"
                            variant="ghost"
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => handleViewFile(file)}
                            title="View File Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          size="sm"
                          variant="ghost"
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-purple-600 hover:text-purple-700"
                          onClick={() => handleCreateReport(file)}
                          title="Create Report"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-blue-600 hover:text-blue-700"
                          onClick={() => handleDownloadData(file)}
                          title="Download Data"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="opacity-0 group-hover:opacity-100 transition-opacity text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteFile(file)}
                          title="Delete File"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal />
    </div>
  )
}
