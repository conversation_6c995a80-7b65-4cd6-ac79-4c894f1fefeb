'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { analyzeDataQuality, applyDataCleaningSuggestion, DataQualityReport, DataCleaningSuggestion } from '@/lib/data-analysis'
import { Badge } from '@/components/ui/badge'
import { 
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Info,
  Sparkles,
  Database,
  RefreshCw
} from 'lucide-react'

export default function DataCleaningDemoPage() {
  const [qualityReport, setQualityReport] = useState<DataQualityReport | null>(null)
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set())
  const [currentData, setCurrentData] = useState<Record<string, any>[]>([])
  const [currentSchema, setCurrentSchema] = useState<Record<string, string>>({})

  // Sample data with various quality issues
  const sampleData = [
    { name: '<PERSON>', age: 25, salary: 50000, department: 'Engineering', email: '<EMAIL>' },
    { name: '<PERSON>', age: null, salary: 60000, department: 'Marketing', email: '<EMAIL>' },
    { name: '', age: 30, salary: 'invalid', department: 'Sales', email: 'invalid-email' },
    { name: 'Bob Johnson', age: 35, salary: 70000, department: 'Engineering', email: '<EMAIL>' },
    { name: 'Alice Brown', age: 28, salary: 55000, department: null, email: '<EMAIL>' },
    { name: 'Charlie Wilson', age: 150, salary: 45000, department: 'HR', email: '<EMAIL>' }, // Outlier age
    { name: 'Diana Prince', age: 32, salary: 65000, department: 'Marketing', email: '<EMAIL>' },
    { name: 'John Doe', age: 25, salary: 50000, department: 'Engineering', email: '<EMAIL>' }, // Duplicate
  ]

  const sampleSchema = {
    name: 'string',
    age: 'number',
    salary: 'number',
    department: 'string',
    email: 'string'
  }

  const runAnalysis = () => {
    setCurrentData([...sampleData])
    setCurrentSchema({ ...sampleSchema })
    const report = analyzeDataQuality(sampleData, sampleSchema)
    setQualityReport(report)
    setAppliedSuggestions(new Set())
  }

  const applySuggestion = (suggestion: DataCleaningSuggestion) => {
    try {
      const result = applyDataCleaningSuggestion(currentData, currentSchema, suggestion)
      setCurrentData(result.data)
      setCurrentSchema(result.schema)
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]))
      
      // Re-analyze with the cleaned data
      const newReport = analyzeDataQuality(result.data, result.schema)
      setQualityReport(newReport)
    } catch (error) {
      console.error('Error applying suggestion:', error)
    }
  }

  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return <XCircle className="h-4 w-4 text-red-500" />
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'low': return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Data Cleaning Demo</h1>
              <p className="text-gray-600">Experience AI-powered data quality analysis and cleaning suggestions</p>
            </div>
            <Button 
              onClick={runAnalysis}
              className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Analyze Sample Data
            </Button>
          </div>
        </div>

        {/* Sample Data Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Database className="h-5 w-5" />
              <span>Sample Dataset</span>
            </CardTitle>
            <CardDescription>
              This dataset contains various data quality issues including missing values, type mismatches, outliers, and duplicates.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    {Object.keys(sampleSchema).map(key => (
                      <th key={key} className="text-left p-2 font-medium text-gray-900">{key}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {sampleData.slice(0, 8).map((row, idx) => (
                    <tr key={idx} className="border-b border-gray-100">
                      {Object.values(row).map((value, cellIdx) => (
                        <td key={cellIdx} className="p-2 text-gray-700">
                          {value === null || value === undefined || value === '' ? (
                            <span className="text-red-400 italic">empty</span>
                          ) : (
                            String(value)
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Analysis Results */}
        {qualityReport && (
          <>
            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-gray-900">{qualityReport.summary.totalIssues}</div>
                  <div className="text-sm text-gray-600">Total Issues</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-red-600">{qualityReport.summary.highSeverityIssues}</div>
                  <div className="text-sm text-gray-600">High Priority</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-yellow-600">{qualityReport.summary.mediumSeverityIssues}</div>
                  <div className="text-sm text-gray-600">Medium Priority</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">{appliedSuggestions.size}</div>
                  <div className="text-sm text-gray-600">Applied Fixes</div>
                </CardContent>
              </Card>
            </div>

            {/* Issues and Suggestions */}
            <div className="space-y-4">
              {qualityReport.issues.map((issue) => {
                const relatedSuggestions = qualityReport.suggestions.filter(s => s.issueId === issue.id)
                
                return (
                  <Card key={issue.id} className="border border-gray-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          {getSeverityIcon(issue.severity)}
                          <div>
                            <CardTitle className="text-lg">{issue.description}</CardTitle>
                            <CardDescription className="mt-1">
                              {issue.column && `Column: ${issue.column} • `}
                              Affects {issue.affectedRows} rows
                            </CardDescription>
                          </div>
                        </div>
                        <Badge className={`${getSeverityColor(issue.severity)} border`}>
                          {issue.severity.toUpperCase()}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    {relatedSuggestions.length > 0 && (
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Sparkles className="h-4 w-4" />
                            <span>AI-Powered Suggestions</span>
                          </div>
                          
                          <div className="space-y-2">
                            {relatedSuggestions.map((suggestion) => {
                              const isApplied = appliedSuggestions.has(suggestion.id)
                              
                              return (
                                <div key={suggestion.id} className={`p-4 rounded-lg border transition-all ${
                                  isApplied ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200 hover:border-gray-300'
                                }`}>
                                  <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                      <div className="flex items-center space-x-2 mb-2">
                                        <h4 className="font-medium text-gray-900">{suggestion.title}</h4>
                                        <span className={`text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                                          {Math.round(suggestion.confidence * 100)}% confidence
                                        </span>
                                        {isApplied && <CheckCircle2 className="h-4 w-4 text-green-600" />}
                                      </div>
                                      <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                                      <p className="text-xs text-gray-500">{suggestion.explanation}</p>
                                    </div>
                                    
                                    <div className="ml-4">
                                      {!isApplied && (
                                        <Button
                                          size="sm"
                                          onClick={() => applySuggestion(suggestion)}
                                          className="text-xs bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                                        >
                                          Apply
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      </CardContent>
                    )}
                  </Card>
                )
              })}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
