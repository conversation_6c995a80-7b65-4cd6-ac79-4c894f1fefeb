'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  BarChart3,

  TrendingUp,
  Table,
  ArrowRight,
  Sparkles,
  Database,

  Settings,

  Share,
  Zap,
  Target,
  Users,
  DollarSign,
  Loader2
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/auth/auth-provider'
import { getUserDataSources, reportsService } from '@/lib/firestore'
import { DataSource as FirebaseDataSource } from '@/lib/database.types'
import { useToast } from '@/components/ui/toast'
import { DataCleaningStep } from '@/components/data-cleaning/data-cleaning-step'

interface ReportTemplate {
  id: string
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  category: 'sales' | 'analytics' | 'financial' | 'operational'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: string
  features: string[]
  color: string
}

interface DataSource {
  id: string
  name: string
  type: 'csv' | 'google_sheets' | 'api'
  records?: number
  lastUpdated: string
  status: 'ready' | 'processing' | 'error'
}

export default function CreateReportPage() {
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null)
  const [selectedDataSource, setSelectedDataSource] = useState<string | null>(null)
  const [step, setStep] = useState(1)
  const [dataSources, setDataSources] = useState<DataSource[]>([])
  const [isLoadingDataSources, setIsLoadingDataSources] = useState(false)
  const [dataSourcesError, setDataSourcesError] = useState<string>('')
  const [isGeneratingReport, setIsGeneratingReport] = useState(false)
  const [reportGenerationError, setReportGenerationError] = useState<string>('')
  const [generatedReportId, setGeneratedReportId] = useState<string | null>(null)
  const [cleanedData, setCleanedData] = useState<Record<string, any>[] | null>(null)
  const [cleanedSchema, setCleanedSchema] = useState<Record<string, string> | null>(null)

  const { user } = useAuth()
  // const router = useRouter()
  const { toast } = useToast() // Commented out as it's not currently used

  // Define report templates as a static configuration (these are template types, not user data)
  const reportTemplates: ReportTemplate[] = [
    {
      id: 'sales-dashboard',
      name: 'Sales Dashboard',
      description: 'Comprehensive sales performance analysis with KPIs and trends',
      icon: TrendingUp,
      category: 'sales',
      difficulty: 'beginner',
      estimatedTime: '5 min',
      features: ['Revenue Tracking', 'Sales Trends', 'Performance Metrics', 'Goal Analysis'],
      color: 'blue'
    },
    {
      id: 'customer-analytics',
      name: 'Customer Analytics',
      description: 'Deep dive into customer behavior and segmentation',
      icon: Users,
      category: 'analytics',
      difficulty: 'intermediate',
      estimatedTime: '8 min',
      features: ['Customer Segments', 'Behavior Analysis', 'Retention Metrics', 'Lifetime Value'],
      color: 'green'
    },
    {
      id: 'financial-summary',
      name: 'Financial Summary',
      description: 'Complete financial overview with P&L and cash flow',
      icon: DollarSign,
      category: 'financial',
      difficulty: 'advanced',
      estimatedTime: '12 min',
      features: ['P&L Statement', 'Cash Flow', 'Budget Analysis', 'Cost Breakdown'],
      color: 'purple'
    },
    {
      id: 'operational-metrics',
      name: 'Operational Metrics',
      description: 'Key operational KPIs and performance indicators',
      icon: Target,
      category: 'operational',
      difficulty: 'intermediate',
      estimatedTime: '7 min',
      features: ['KPI Tracking', 'Efficiency Metrics', 'Resource Utilization', 'Process Analysis'],
      color: 'orange'
    },
    {
      id: 'data-overview',
      name: 'Data Overview',
      description: 'Simple data visualization and summary statistics',
      icon: BarChart3,
      category: 'analytics',
      difficulty: 'beginner',
      estimatedTime: '3 min',
      features: ['Data Summary', 'Basic Charts', 'Key Statistics', 'Data Quality'],
      color: 'cyan'
    },
    {
      id: 'custom-report',
      name: 'Custom Report',
      description: 'Build your own report from scratch with full customization',
      icon: Settings,
      category: 'analytics',
      difficulty: 'advanced',
      estimatedTime: '15 min',
      features: ['Custom Charts', 'Advanced Filters', 'Custom Metrics', 'Flexible Layout'],
      color: 'gray'
    }
  ]

  // Load user's data sources
  useEffect(() => {
    const loadDataSources = async () => {
      if (!user) return

      try {
        setIsLoadingDataSources(true)
        setDataSourcesError('')

        const firebaseDataSources = await getUserDataSources(user.uid)

        // Transform Firebase data sources to match our interface
        const transformedDataSources: DataSource[] = firebaseDataSources.map((source: FirebaseDataSource) => {
          // Calculate time since last update
          const lastSync = source.last_sync ? new Date(source.last_sync) : new Date(source.created_at)
          const now = new Date()
          const diffInHours = Math.floor((now.getTime() - lastSync.getTime()) / (1000 * 60 * 60))

          let lastUpdated: string
          if (diffInHours < 1) {
            lastUpdated = 'Just now'
          } else if (diffInHours < 24) {
            lastUpdated = `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`
          } else {
            const diffInDays = Math.floor(diffInHours / 24)
            lastUpdated = `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
          }

          return {
            id: source.id,
            name: source.name,
            type: source.type,
            records: undefined, // We don't store record count in Firebase yet
            lastUpdated,
            status: source.status === 'active' ? 'ready' : source.status === 'error' ? 'error' : 'processing'
          }
        })

        setDataSources(transformedDataSources)

        // Check for pre-selected data source from localStorage
        const preSelectedDataSource = localStorage.getItem('selectedDataSource')
        if (preSelectedDataSource) {
          try {
            const parsedDataSource = JSON.parse(preSelectedDataSource)
            const matchingDataSource = transformedDataSources.find(ds => ds.id === parsedDataSource.id)

            if (matchingDataSource) {
              setSelectedDataSource(parsedDataSource.id)
              setStep(2) // Skip to data source selection step
              console.log(`✅ Pre-selected data source: ${parsedDataSource.name}`)

              toast({
                type: 'success',
                title: 'Data Source Selected',
                description: `Ready to create a report with data from "${parsedDataSource.name}"`,
                duration: 4000
              })
            }

            // Clear the localStorage after using it
            localStorage.removeItem('selectedDataSource')
          } catch (error) {
            console.error('Failed to parse pre-selected data source:', error)
            localStorage.removeItem('selectedDataSource')
          }
        }
      } catch (error) {
        console.error('Failed to load data sources:', error)
        setDataSourcesError('Failed to load your data sources. Please try again.')
      } finally {
        setIsLoadingDataSources(false)
      }
    }

    loadDataSources()
  }, [user])

  // Handle data cleaning completion
  const handleDataCleaningComplete = (data: Record<string, any>[], schema: Record<string, string>) => {
    setCleanedData(data)
    setCleanedSchema(schema)
    setStep(4) // Move to report generation step
  }

  // Generate report function
  const handleGenerateReport = async () => {
    if (!user || !selectedTemplate || !selectedDataSource) return

    try {
      setIsGeneratingReport(true)
      setReportGenerationError('')

      // Find the selected template and data source
      const template = reportTemplates.find(t => t.id === selectedTemplate)
      const dataSource = dataSources.find(ds => ds.id === selectedDataSource)

      if (!template || !dataSource) {
        throw new Error('Selected template or data source not found')
      }

      // Create report configuration
      const reportConfig = {
        template_id: template.id,
        template_name: template.name,
        data_source_id: dataSource.id,
        data_source_name: dataSource.name,
        data_source_type: dataSource.type,
        chart_types: template.features,
        created_with_wizard: true,
        // Include cleaned data information if available
        ...(cleanedData && cleanedSchema && {
          data_cleaning_applied: true,
          cleaned_rows: cleanedData.length,
          cleaned_columns: Object.keys(cleanedSchema).length
        })
      }

      // Create the report in Firebase
      const reportData = {
        user_id: user.uid,
        dataset_id: dataSource.id, // Using data source ID as dataset ID for now
        name: `${template.name} - ${dataSource.name}`,
        description: `${template.description} using data from ${dataSource.name}${cleanedData ? ' (with data cleaning applied)' : ''}`,
        type: template.category,
        config: reportConfig,
        status: 'published', // Mark as completed
        is_template: false
      }

      console.log('Creating report with data:', reportData)
      const reportId = await reportsService.create(reportData)

      setGeneratedReportId(reportId)

      // Move to completion step after a short delay to show progress
      setTimeout(() => {
        setStep(5) // Updated completion step number
      }, 2000)

    } catch (error) {
      console.error('Error generating report:', error)
      setReportGenerationError(error instanceof Error ? error.message : 'Failed to generate report')
    } finally {
      setIsGeneratingReport(false)
    }
  }

  const getTemplateIcon = (template: ReportTemplate) => {
    const Icon = template.icon
    const colorClasses = {
      blue: 'from-blue-500 to-cyan-600',
      green: 'from-emerald-500 to-teal-600',
      purple: 'from-purple-500 to-pink-600',
      orange: 'from-orange-500 to-red-600',
      cyan: 'from-cyan-500 to-blue-600',
      gray: 'from-gray-500 to-slate-600'
    }
    return (
      <div className={`p-3 rounded-xl bg-gradient-to-br ${colorClasses[template.color as keyof typeof colorClasses]} group-hover:scale-110 transition-transform duration-300`}>
        <Icon className="h-6 w-6 text-white" />
      </div>
    )
  }

  const getDifficultyBadge = (difficulty: string) => {
    const styles = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    }
    return (
      <Badge className={`${styles[difficulty as keyof typeof styles]} hover:${styles[difficulty as keyof typeof styles]}`}>
        {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
      </Badge>
    )
  }

  const getDataSourceIcon = (type: string) => {
    switch (type) {
      case 'csv':
        return <FileText className="h-5 w-5 text-green-600" />
      case 'google_sheets':
        return <Table className="h-5 w-5 text-green-600" />
      case 'firebase_sync':
        return <Database className="h-5 w-5 text-orange-600" />
      case 'api':
        return <Database className="h-5 w-5 text-purple-600" />
      default:
        return <FileText className="h-5 w-5 text-gray-600" />
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <div className="container mx-auto max-w-7xl px-6 py-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-8 mb-8">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>
          <div className="relative z-10">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm font-medium">Templates Ready</span>
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
                Create Reports,
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-purple-400">
                  Instantly
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                Choose from professional templates or build custom reports to transform your data into actionable insights
              </p>
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Sparkles className="h-4 w-4 text-purple-400" />
                  <span>AI-Powered Templates</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="h-4 w-4 text-yellow-400" />
                  <span>Instant Generation</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Share className="h-4 w-4 text-blue-400" />
                  <span>Easy Sharing</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className={`flex items-center space-x-3 ${step >= 1 ? 'text-purple-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                  1
                </div>
                <span className="font-medium">Choose Template</span>
              </div>
              <div className={`w-12 h-0.5 ${step >= 2 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center space-x-3 ${step >= 2 ? 'text-purple-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                  2
                </div>
                <span className="font-medium">Select Data</span>
              </div>
              <div className={`w-12 h-0.5 ${step >= 3 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center space-x-3 ${step >= 3 ? 'text-purple-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                  3
                </div>
                <span className="font-medium">Clean Data</span>
              </div>
              <div className={`w-12 h-0.5 ${step >= 4 ? 'bg-purple-600' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center space-x-3 ${step >= 4 ? 'text-purple-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 4 ? 'bg-purple-600 text-white' : 'bg-gray-200'}`}>
                  4
                </div>
                <span className="font-medium">Generate Report</span>
              </div>
            </div>
          </div>
        </div>

        {/* Step 1: Template Selection */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose a Report Template</h2>
                <p className="text-sm text-gray-600">Select a template that best fits your reporting needs</p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {reportTemplates.map((template) => (
                  <div
                    key={template.id}
                    className={`group bg-gray-50 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-lg ${
                      selectedTemplate === template.id ? 'ring-2 ring-purple-500 bg-purple-50' : 'hover:bg-gray-100'
                    }`}
                    onClick={() => setSelectedTemplate(template.id)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      {getTemplateIcon(template)}
                      <div className="flex items-center space-x-2">
                        {getDifficultyBadge(template.difficulty)}
                      </div>
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
                    <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Estimated time:</span>
                        <span className="font-medium text-gray-900">{template.estimatedTime}</span>
                      </div>
                      
                      <div className="space-y-2">
                        {template.features.slice(0, 2).map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2 text-sm text-gray-700">
                            <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                            <span>{feature}</span>
                          </div>
                        ))}
                        {template.features.length > 2 && (
                          <div className="text-xs text-gray-500">
                            +{template.features.length - 2} more features
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {selectedTemplate && (
                <div className="mt-8 flex justify-end">
                  <Button 
                    onClick={() => setStep(2)}
                    className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  >
                    Continue to Data Selection
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Step 2: Data Source Selection */}
        {step === 2 && (
          <div className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Select Data Source</h2>
                <p className="text-sm text-gray-600">Choose the data file you want to use for your report</p>
              </div>
              
              <div className="space-y-4">
                {isLoadingDataSources ? (
                  <div className="text-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
                    <p className="text-sm text-gray-600">Loading your data sources...</p>
                  </div>
                ) : dataSourcesError ? (
                  <div className="text-center py-8">
                    <div className="text-red-600 mb-4">
                      <Database className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">{dataSourcesError}</p>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => window.location.reload()}
                      className="text-sm"
                    >
                      Try Again
                    </Button>
                  </div>
                ) : dataSources.length === 0 ? (
                  <div className="text-center py-8">
                    <Database className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                    <p className="text-sm text-gray-600 mb-4">No data sources found</p>
                    <p className="text-xs text-gray-500 mb-4">
                      You need to import some data first before creating reports.
                    </p>
                    <Link href="/dashboard/import">
                      <Button variant="outline" className="text-sm">
                        Import Data
                      </Button>
                    </Link>
                  </div>
                ) : (
                  dataSources.map((source) => (
                    <div
                      key={source.id}
                      className={`flex items-center justify-between p-4 rounded-xl cursor-pointer transition-all duration-300 ${
                        selectedDataSource === source.id
                          ? 'bg-purple-50 border-2 border-purple-500'
                          : 'bg-gray-50 hover:bg-gray-100 border-2 border-transparent'
                      }`}
                      onClick={() => setSelectedDataSource(source.id)}
                    >
                      <div className="flex items-center space-x-4">
                        {getDataSourceIcon(source.type)}
                        <div>
                          <p className="font-medium text-gray-900">{source.name}</p>
                          <p className="text-sm text-gray-500">
                            {source.records ? `${source.records.toLocaleString()} records • ` : ''}Updated {source.lastUpdated}
                          </p>
                        </div>
                      </div>
                      <Badge className={`${
                        source.status === 'ready' ? 'bg-green-100 text-green-800' :
                        source.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      } hover:bg-current`}>
                        {source.status === 'ready' ? 'Ready' :
                         source.status === 'processing' ? 'Processing' : 'Error'}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
              
              <div className="mt-8 flex justify-between">
                <Button 
                  variant="outline" 
                  onClick={() => setStep(1)}
                  className="rounded-xl"
                >
                  Back to Templates
                </Button>
                {selectedDataSource && (
                  <Button
                    onClick={() => setStep(3)}
                    className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  >
                    Continue to Data Cleaning
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Data Cleaning */}
        {step === 3 && selectedDataSource && (
          <DataCleaningStep
            dataSource={{
              id: selectedDataSource,
              name: dataSources.find(ds => ds.id === selectedDataSource)?.name || 'Unknown',
              type: dataSources.find(ds => ds.id === selectedDataSource)?.type || 'csv'
            }}
            onContinue={handleDataCleaningComplete}
            onBack={() => setStep(2)}
          />
        )}

        {/* Step 4: Report Generation */}
        {step === 4 && (
          <div className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              {!isGeneratingReport && !generatedReportId && !reportGenerationError && (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                    <Sparkles className="h-10 w-10 text-white" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-900 mb-4">Ready to Generate Report</h2>
                  <p className="text-gray-600 mb-8">
                    {cleanedData ?
                      `Your data has been cleaned and is ready for analysis. We'll create a ${reportTemplates.find(t => t.id === selectedTemplate)?.name} using ${cleanedData.length} rows of clean data.` :
                      `We'll create a ${reportTemplates.find(t => t.id === selectedTemplate)?.name} using your selected data source.`
                    }
                  </p>
                  <div className="flex justify-center space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => setStep(3)}
                      className="rounded-xl"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Data Cleaning
                    </Button>
                    <Button
                      onClick={handleGenerateReport}
                      className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      Generate Report
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {isGeneratingReport && (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                    <Loader2 className="h-10 w-10 text-white animate-spin" />
                  </div>
                  <h2 className="text-2xl font-semibold text-gray-900 mb-4">Generating Your Report</h2>
                  <p className="text-gray-600 mb-8">
                    We&apos;re analyzing your {cleanedData ? 'cleaned ' : ''}data and creating a professional report. This will take just a moment...
                  </p>
                  <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto mb-6">
                    <div className="h-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full animate-pulse" style={{width: '75%'}}></div>
                  </div>
                </div>
              )}

              <div className="text-center py-12">
                {reportGenerationError ? (
                  <>
                    <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                      <Loader2 className="h-10 w-10 text-white animate-spin" />
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-4">Generating Your Report</h2>
                    <p className="text-gray-600 mb-8">
                      We&apos;re analyzing your data and creating a professional report. This will take just a moment...
                    </p>
                    <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto mb-6">
                      <div className="h-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full animate-pulse" style={{width: '75%'}}></div>
                    </div>
                  </>
                ) : reportGenerationError ? (
                  <>
                    <div className="w-20 h-20 bg-red-100 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                      <span className="text-2xl">❌</span>
                    </div>
                    <h2 className="text-2xl font-semibold text-gray-900 mb-4">Generation Failed</h2>
                    <p className="text-red-600 mb-8">{reportGenerationError}</p>
                    <div className="flex justify-center space-x-4">
                      <Button
                        variant="outline"
                        onClick={() => setStep(3)}
                        className="rounded-xl"
                      >
                        Back to Data Cleaning
                      </Button>
                      <Button
                        onClick={handleGenerateReport}
                        className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                      >
                        Try Again
                      </Button>
                    </div>
                  </>
                ) : null}
              </div>
            </div>
          </div>
        )}

        {/* Step 5: Report Completion */}
        {step === 5 && generatedReportId && (
          <div className="space-y-6">
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300">
              <div className="text-center py-12">
                <div className="w-20 h-20 bg-green-100 rounded-2xl mx-auto mb-6 flex items-center justify-center">
                  <span className="text-2xl">✅</span>
                </div>
                <h2 className="text-2xl font-semibold text-gray-900 mb-4">Report Generated Successfully!</h2>
                <p className="text-gray-600 mb-8">
                  Your report has been created and is ready to view. You can find it in your reports dashboard.
                </p>
                <div className="flex justify-center space-x-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setStep(1)
                      setSelectedTemplate(null)
                      setSelectedDataSource(null)
                      setGeneratedReportId(null)
                      setReportGenerationError('')
                    }}
                    className="rounded-xl"
                  >
                    Create Another Report
                  </Button>
                  <Link href="/dashboard/reports">
                    <Button className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                      View All Reports
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
