export interface DataAnalysis {
  summary: {
    totalRows: number
    totalColumns: number
    numericColumns: string[]
    categoricalColumns: string[]
    dateColumns: string[]
    nullPercentage: number
  }
  insights: string[]
  recommendedCharts: ChartRecommendation[]
  statistics: Record<string, ColumnStatistics>
}

export interface DataQualityIssue {
  id: string
  type: 'missing_values' | 'inconsistent_format' | 'duplicates' | 'outliers' | 'naming' | 'type_mismatch'
  severity: 'low' | 'medium' | 'high'
  column?: string
  description: string
  affectedRows: number
  examples?: any[]
}

export interface DataCleaningSuggestion {
  id: string
  issueId: string
  type: 'fill_missing' | 'remove_missing' | 'interpolate' | 'standardize_format' | 'remove_duplicates' | 'handle_outliers' | 'rename_column' | 'convert_type'
  title: string
  description: string
  explanation: string
  confidence: number
  parameters?: Record<string, any>
  preview?: {
    before: any[]
    after: any[]
  }
}

export interface DataQualityReport {
  issues: DataQualityIssue[]
  suggestions: DataCleaningSuggestion[]
  summary: {
    totalIssues: number
    highSeverityIssues: number
    mediumSeverityIssues: number
    lowSeverityIssues: number
    affectedRows: number
    affectedColumns: string[]
  }
}

export interface ChartRecommendation {
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'histogram'
  title: string
  description: string
  xColumn?: string
  yColumn?: string
  priority: number
  reason: string
}

export interface ColumnStatistics {
  type: 'numeric' | 'categorical' | 'date'
  uniqueValues: number
  nullCount: number
  min?: number
  max?: number
  mean?: number
  median?: number
  mode?: string | number
  distribution?: Record<string, number>
}

export function analyzeData(data: Record<string, any>[], schema: Record<string, string>): DataAnalysis {
  if (!data.length) {
    return {
      summary: {
        totalRows: 0,
        totalColumns: 0,
        numericColumns: [],
        categoricalColumns: [],
        dateColumns: [],
        nullPercentage: 0
      },
      insights: ['No data available for analysis'],
      recommendedCharts: [],
      statistics: {}
    }
  }

  // Categorize columns
  const numericColumns = Object.keys(schema).filter(col => 
    schema[col] === 'number' || schema[col] === 'integer'
  )
  const categoricalColumns = Object.keys(schema).filter(col => 
    schema[col] === 'string'
  )
  const dateColumns = Object.keys(schema).filter(col => 
    schema[col] === 'date'
  )

  // Calculate statistics for each column
  const statistics: Record<string, ColumnStatistics> = {}
  const totalCells = data.length * Object.keys(schema).length
  let totalNulls = 0

  Object.keys(schema).forEach(column => {
    const values = data.map(row => row[column]).filter(val => val !== null && val !== undefined && val !== '')
    const nullCount = data.length - values.length
    totalNulls += nullCount

    if (schema[column] === 'number' || schema[column] === 'integer') {
      const numericValues = values.map(v => Number(v)).filter(v => !isNaN(v))
      const sorted = numericValues.sort((a, b) => a - b)
      
      statistics[column] = {
        type: 'numeric',
        uniqueValues: new Set(numericValues).size,
        nullCount,
        min: sorted.length > 0 ? sorted[0] : undefined,
        max: sorted.length > 0 ? sorted[sorted.length - 1] : undefined,
        mean: sorted.length > 0 ? sorted.reduce((a, b) => a + b, 0) / sorted.length : undefined,
        median: sorted.length > 0 ? sorted[Math.floor(sorted.length / 2)] : undefined
      }
    } else if (schema[column] === 'string') {
      const distribution = values.reduce((acc, val) => {
        acc[val] = (acc[val] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      
      const mostCommon = Object.entries(distribution).sort(([,a], [,b]) => b - a)[0]
      
      statistics[column] = {
        type: 'categorical',
        uniqueValues: new Set(values).size,
        nullCount,
        mode: mostCommon ? mostCommon[0] : undefined,
        distribution
      }
    } else {
      statistics[column] = {
        type: 'date',
        uniqueValues: new Set(values).size,
        nullCount
      }
    }
  })

  const nullPercentage = (totalNulls / totalCells) * 100

  // Generate insights
  const insights = generateInsights(data, schema, statistics, numericColumns, categoricalColumns, dateColumns)

  // Generate chart recommendations
  const recommendedCharts = generateChartRecommendations(data, schema, statistics, numericColumns, categoricalColumns, dateColumns)

  return {
    summary: {
      totalRows: data.length,
      totalColumns: Object.keys(schema).length,
      numericColumns,
      categoricalColumns,
      dateColumns,
      nullPercentage
    },
    insights,
    recommendedCharts,
    statistics
  }
}

function generateInsights(
  data: Record<string, any>[], 
  schema: Record<string, string>,
  statistics: Record<string, ColumnStatistics>,
  numericColumns: string[],
  categoricalColumns: string[],
  dateColumns: string[]
): string[] {
  const insights = []

  // Basic data insights
  insights.push(`Dataset contains ${data.length.toLocaleString()} records across ${Object.keys(schema).length} columns`)

  if (numericColumns.length > 0) {
    insights.push(`Found ${numericColumns.length} numeric columns suitable for quantitative analysis`)
    
    // Find columns with high variance
    const highVarianceColumns = numericColumns.filter(col => {
      const stats = statistics[col]
      if (stats.min !== undefined && stats.max !== undefined && stats.mean !== undefined) {
        const range = stats.max - stats.min
        return range > stats.mean * 2 // High variance indicator
      }
      return false
    })
    
    if (highVarianceColumns.length > 0) {
      insights.push(`Columns with high variance detected: ${highVarianceColumns.join(', ')} - good for trend analysis`)
    }
  }

  if (categoricalColumns.length > 0) {
    insights.push(`Found ${categoricalColumns.length} categorical columns for segmentation and grouping`)
    
    // Find columns with reasonable number of categories for visualization
    const goodCategoricalColumns = categoricalColumns.filter(col => {
      const uniqueValues = statistics[col].uniqueValues
      return uniqueValues >= 2 && uniqueValues <= 20 // Sweet spot for visualization
    })
    
    if (goodCategoricalColumns.length > 0) {
      insights.push(`${goodCategoricalColumns.length} categorical columns are well-suited for charts`)
    }
  }

  if (dateColumns.length > 0) {
    insights.push(`Found ${dateColumns.length} date columns - perfect for time-series analysis`)
  }

  // Data quality insights
  const nullPercentage = Object.values(statistics).reduce((sum, stat) => sum + stat.nullCount, 0) / (data.length * Object.keys(schema).length) * 100
  
  if (nullPercentage > 10) {
    insights.push(`⚠️ ${nullPercentage.toFixed(1)}% of data is missing - consider data cleaning`)
  } else if (nullPercentage < 5) {
    insights.push(`✅ High data quality with only ${nullPercentage.toFixed(1)}% missing values`)
  }

  return insights
}

function generateChartRecommendations(
  data: Record<string, any>[], 
  schema: Record<string, string>,
  statistics: Record<string, ColumnStatistics>,
  numericColumns: string[],
  categoricalColumns: string[],
  dateColumns: string[]
): ChartRecommendation[] {
  const recommendations: ChartRecommendation[] = []

  // Bar chart recommendations
  if (categoricalColumns.length > 0 && numericColumns.length > 0) {
    const bestCategorical = categoricalColumns.find(col => 
      statistics[col].uniqueValues >= 2 && statistics[col].uniqueValues <= 15
    )
    const bestNumeric = numericColumns[0] // Use first numeric column
    
    if (bestCategorical && bestNumeric) {
      recommendations.push({
        type: 'bar',
        title: `${bestNumeric} by ${bestCategorical}`,
        description: `Compare ${bestNumeric} values across different ${bestCategorical} categories`,
        xColumn: bestCategorical,
        yColumn: bestNumeric,
        priority: 9,
        reason: 'Categorical vs Numeric comparison'
      })
    }
  }

  // Pie chart recommendations
  if (categoricalColumns.length > 0) {
    const bestCategorical = categoricalColumns.find(col => 
      statistics[col].uniqueValues >= 2 && statistics[col].uniqueValues <= 8
    )
    
    if (bestCategorical) {
      recommendations.push({
        type: 'pie',
        title: `Distribution of ${bestCategorical}`,
        description: `Show the proportion of each ${bestCategorical} category`,
        xColumn: bestCategorical,
        priority: 8,
        reason: 'Categorical distribution analysis'
      })
    }
  }

  // Line chart recommendations
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    recommendations.push({
      type: 'line',
      title: `${numericColumns[0]} over Time`,
      description: `Track ${numericColumns[0]} trends over time`,
      xColumn: dateColumns[0],
      yColumn: numericColumns[0],
      priority: 10,
      reason: 'Time-series analysis'
    })
  } else if (numericColumns.length >= 2) {
    recommendations.push({
      type: 'line',
      title: `${numericColumns[1]} vs ${numericColumns[0]}`,
      description: `Show relationship between ${numericColumns[0]} and ${numericColumns[1]}`,
      xColumn: numericColumns[0],
      yColumn: numericColumns[1],
      priority: 7,
      reason: 'Numeric correlation analysis'
    })
  }

  // Area chart for time series
  if (dateColumns.length > 0 && numericColumns.length > 0) {
    recommendations.push({
      type: 'area',
      title: `${numericColumns[0]} Trend Area`,
      description: `Visualize ${numericColumns[0]} volume over time`,
      xColumn: dateColumns[0],
      yColumn: numericColumns[0],
      priority: 6,
      reason: 'Time-series volume analysis'
    })
  }

  // Sort by priority
  return recommendations.sort((a, b) => b.priority - a.priority)
}

export function prepareChartData(
  data: Record<string, any>[],
  type: string,
  xColumn?: string,
  yColumn?: string
): any[] {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return [{ name: 'No Data', value: 0 }]
  }

  try {
    switch (type) {
      case 'bar':
      case 'line':
      case 'area':
        if (xColumn && yColumn) {
          // Group by X column and aggregate Y column
          const grouped = data.reduce((acc, row) => {
            if (!row || typeof row !== 'object') return acc
            const key = String(row[xColumn] || 'Unknown')
            if (!acc[key]) acc[key] = []
            const value = Number(row[yColumn]) || 0
            acc[key].push(value)
            return acc
          }, {} as Record<string, number[]>)

          const result = Object.entries(grouped).map(([key, values]) => ({
            name: key,
            value: values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0, // Average
            total: values.reduce((sum, val) => sum + val, 0), // Sum
            count: values.length
          }))

          // Ensure we have at least some data
          return result.length > 0 ? result : [{ name: 'No Data', value: 0 }]
        } else {
          // Fallback when columns are not specified
          return [{ name: 'No Data', value: 0 }]
        }

      case 'pie':
        if (xColumn) {
          const counts = data.reduce((acc, row) => {
            if (!row || typeof row !== 'object') return acc
            const key = String(row[xColumn] || 'Unknown')
            acc[key] = (acc[key] || 0) + 1
            return acc
          }, {} as Record<string, number>)

          const result = Object.entries(counts).map(([key, value]) => ({
            name: key,
            value: value
          }))

          // Ensure we have at least some data
          return result.length > 0 ? result : [{ name: 'No Data', value: 1 }]
        } else {
          // Fallback when column is not specified
          return [{ name: 'No Data', value: 1 }]
        }

      default:
        return [{ name: 'Unsupported Chart Type', value: 0 }]
    }
  } catch (error) {
    console.error('Error preparing chart data:', error)
    return [{ name: 'Error', value: 0 }]
  }
}

export function analyzeDataQuality(data: Record<string, any>[], schema: Record<string, string>): DataQualityReport {
  const issues: DataQualityIssue[] = []
  const suggestions: DataCleaningSuggestion[] = []

  if (!data.length) {
    return {
      issues: [],
      suggestions: [],
      summary: {
        totalIssues: 0,
        highSeverityIssues: 0,
        mediumSeverityIssues: 0,
        lowSeverityIssues: 0,
        affectedRows: 0,
        affectedColumns: []
      }
    }
  }

  // Analyze each column
  Object.keys(schema).forEach(column => {
    const columnData = data.map(row => row[column])

    // Check for missing values
    const missingCount = columnData.filter(val => val === null || val === undefined || val === '').length
    if (missingCount > 0) {
      const missingPercentage = (missingCount / data.length) * 100
      const severity = missingPercentage > 50 ? 'high' : missingPercentage > 20 ? 'medium' : 'low'

      const issue: DataQualityIssue = {
        id: `missing_${column}`,
        type: 'missing_values',
        severity,
        column,
        description: `${missingCount} missing values (${missingPercentage.toFixed(1)}%) in column "${column}"`,
        affectedRows: missingCount,
        examples: data.filter(row => row[column] === null || row[column] === undefined || row[column] === '').slice(0, 3)
      }
      issues.push(issue)

      // Generate suggestions for handling missing values
      if (schema[column] === 'number') {
        suggestions.push({
          id: `fill_mean_${column}`,
          issueId: issue.id,
          type: 'fill_missing',
          title: 'Fill with Mean',
          description: `Replace missing values with the column mean`,
          explanation: `This approach works well for numeric data when missing values are random and the mean is representative.`,
          confidence: 0.8,
          parameters: { method: 'mean', column }
        })

        suggestions.push({
          id: `interpolate_${column}`,
          issueId: issue.id,
          type: 'interpolate',
          title: 'Linear Interpolation',
          description: `Use linear interpolation to estimate missing values`,
          explanation: `Good for time-series or ordered data where values follow a trend.`,
          confidence: 0.7,
          parameters: { method: 'linear', column }
        })
      } else if (schema[column] === 'string') {
        suggestions.push({
          id: `fill_mode_${column}`,
          issueId: issue.id,
          type: 'fill_missing',
          title: 'Fill with Most Common',
          description: `Replace missing values with the most frequent value`,
          explanation: `Uses the mode (most common value) to fill gaps in categorical data.`,
          confidence: 0.7,
          parameters: { method: 'mode', column }
        })
      }

      suggestions.push({
        id: `remove_missing_${column}`,
        issueId: issue.id,
        type: 'remove_missing',
        title: 'Remove Rows',
        description: `Remove rows with missing values in this column`,
        explanation: `Best when missing data is minimal and removing rows won't significantly impact analysis.`,
        confidence: missingPercentage < 10 ? 0.8 : 0.4,
        parameters: { column }
      })
    }

    // Check for data type inconsistencies
    if (schema[column] === 'number') {
      const nonNumericValues = columnData.filter(val =>
        val !== null && val !== undefined && val !== '' &&
        (isNaN(Number(val)) || !isFinite(Number(val)))
      )

      if (nonNumericValues.length > 0) {
        const issue: DataQualityIssue = {
          id: `type_mismatch_${column}`,
          type: 'type_mismatch',
          severity: 'medium',
          column,
          description: `${nonNumericValues.length} non-numeric values in numeric column "${column}"`,
          affectedRows: nonNumericValues.length,
          examples: nonNumericValues.slice(0, 3)
        }
        issues.push(issue)

        suggestions.push({
          id: `convert_numeric_${column}`,
          issueId: issue.id,
          type: 'convert_type',
          title: 'Convert to Numbers',
          description: `Attempt to convert text values to numbers where possible`,
          explanation: `Removes non-numeric characters and converts valid numeric strings to numbers.`,
          confidence: 0.8,
          parameters: { targetType: 'number', column }
        })
      }
    }

    // Check for outliers in numeric columns
    if (schema[column] === 'number') {
      const numericValues = columnData
        .filter(val => val !== null && val !== undefined && val !== '')
        .map(val => Number(val))
        .filter(val => !isNaN(val) && isFinite(val))

      if (numericValues.length > 0) {
        const sorted = numericValues.sort((a, b) => a - b)
        const q1 = sorted[Math.floor(sorted.length * 0.25)]
        const q3 = sorted[Math.floor(sorted.length * 0.75)]
        const iqr = q3 - q1
        const lowerBound = q1 - 1.5 * iqr
        const upperBound = q3 + 1.5 * iqr

        const outliers = numericValues.filter(val => val < lowerBound || val > upperBound)

        if (outliers.length > 0) {
          const outlierPercentage = (outliers.length / numericValues.length) * 100
          const severity = outlierPercentage > 10 ? 'high' : outlierPercentage > 5 ? 'medium' : 'low'

          const issue: DataQualityIssue = {
            id: `outliers_${column}`,
            type: 'outliers',
            severity,
            column,
            description: `${outliers.length} potential outliers detected in column "${column}"`,
            affectedRows: outliers.length,
            examples: outliers.slice(0, 3)
          }
          issues.push(issue)

          suggestions.push({
            id: `cap_outliers_${column}`,
            issueId: issue.id,
            type: 'handle_outliers',
            title: 'Cap Outliers',
            description: `Cap extreme values to the 5th and 95th percentiles`,
            explanation: `Reduces the impact of extreme values while preserving the overall distribution.`,
            confidence: 0.7,
            parameters: { method: 'cap', column, lowerBound, upperBound }
          })

          suggestions.push({
            id: `remove_outliers_${column}`,
            issueId: issue.id,
            type: 'handle_outliers',
            title: 'Remove Outliers',
            description: `Remove rows with outlier values`,
            explanation: `Best when outliers are clearly erroneous and removing them won't significantly impact analysis.`,
            confidence: outlierPercentage < 5 ? 0.6 : 0.3,
            parameters: { method: 'remove', column, lowerBound, upperBound }
          })
        }
      }
    }
  })

  // Check for duplicate rows
  const duplicateRows = findDuplicateRows(data)
  if (duplicateRows.length > 0) {
    const issue: DataQualityIssue = {
      id: 'duplicate_rows',
      type: 'duplicates',
      severity: duplicateRows.length > data.length * 0.1 ? 'high' : 'medium',
      description: `${duplicateRows.length} duplicate rows found`,
      affectedRows: duplicateRows.length,
      examples: duplicateRows.slice(0, 3)
    }
    issues.push(issue)

    suggestions.push({
      id: 'remove_duplicates',
      issueId: issue.id,
      type: 'remove_duplicates',
      title: 'Remove Duplicates',
      description: `Remove duplicate rows, keeping only the first occurrence`,
      explanation: `Eliminates redundant data that could skew analysis results.`,
      confidence: 0.9,
      parameters: { method: 'first' }
    })
  }

  // Check for column naming issues
  Object.keys(schema).forEach(column => {
    const namingIssues = analyzeColumnNaming(column)
    if (namingIssues.length > 0) {
      namingIssues.forEach(issue => {
        issues.push({
          id: `naming_${column}`,
          type: 'naming',
          severity: 'low',
          column,
          description: issue.description,
          affectedRows: 0,
          examples: []
        })

        suggestions.push({
          id: `rename_${column}`,
          issueId: `naming_${column}`,
          type: 'rename_column',
          title: 'Rename Column',
          description: `Rename to "${issue.suggestion}"`,
          explanation: issue.explanation,
          confidence: 0.6,
          parameters: { oldName: column, newName: issue.suggestion }
        })
      })
    }
  })

  const affectedColumns = [...new Set(issues.filter(i => i.column).map(i => i.column!))]
  const totalAffectedRows = issues.reduce((sum, issue) => sum + issue.affectedRows, 0)

  return {
    issues,
    suggestions,
    summary: {
      totalIssues: issues.length,
      highSeverityIssues: issues.filter(i => i.severity === 'high').length,
      mediumSeverityIssues: issues.filter(i => i.severity === 'medium').length,
      lowSeverityIssues: issues.filter(i => i.severity === 'low').length,
      affectedRows: Math.min(totalAffectedRows, data.length), // Cap at total rows
      affectedColumns
    }
  }
}

function findDuplicateRows(data: Record<string, any>[]): Record<string, any>[] {
  const seen = new Set<string>()
  const duplicates: Record<string, any>[] = []

  data.forEach(row => {
    const rowString = JSON.stringify(row)
    if (seen.has(rowString)) {
      duplicates.push(row)
    } else {
      seen.add(rowString)
    }
  })

  return duplicates
}

function analyzeColumnNaming(columnName: string): Array<{description: string, suggestion: string, explanation: string}> {
  const issues: Array<{description: string, suggestion: string, explanation: string}> = []

  // Check for spaces and special characters
  if (/\s/.test(columnName)) {
    const suggestion = columnName.replace(/\s+/g, '_').toLowerCase()
    issues.push({
      description: `Column name "${columnName}" contains spaces`,
      suggestion,
      explanation: 'Replacing spaces with underscores improves compatibility with analysis tools.'
    })
  }

  // Check for mixed case
  if (/[A-Z]/.test(columnName) && /[a-z]/.test(columnName) && !columnName.includes('_')) {
    const suggestion = columnName.replace(/([A-Z])/g, '_$1').toLowerCase().replace(/^_/, '')
    issues.push({
      description: `Column name "${columnName}" uses mixed case`,
      suggestion,
      explanation: 'Converting to snake_case improves readability and consistency.'
    })
  }

  // Check for very short names
  if (columnName.length <= 2 && !['id', 'ID'].includes(columnName)) {
    const suggestion = `${columnName}_value`
    issues.push({
      description: `Column name "${columnName}" is very short`,
      suggestion,
      explanation: 'Longer, descriptive names make data easier to understand and work with.'
    })
  }

  return issues
}

export function applyDataCleaningSuggestion(
  data: Record<string, any>[],
  schema: Record<string, string>,
  suggestion: DataCleaningSuggestion
): { data: Record<string, any>[], schema: Record<string, string> } {
  let cleanedData = [...data]
  let cleanedSchema = { ...schema }

  switch (suggestion.type) {
    case 'fill_missing':
      cleanedData = fillMissingValues(cleanedData, suggestion.parameters!)
      break

    case 'remove_missing':
      cleanedData = removeMissingValues(cleanedData, suggestion.parameters!)
      break

    case 'interpolate':
      cleanedData = interpolateValues(cleanedData, suggestion.parameters!)
      break

    case 'standardize_format':
      cleanedData = standardizeFormat(cleanedData, suggestion.parameters!)
      break

    case 'remove_duplicates':
      cleanedData = removeDuplicates(cleanedData)
      break

    case 'handle_outliers':
      cleanedData = handleOutliers(cleanedData, suggestion.parameters!)
      break

    case 'rename_column':
      const result = renameColumn(cleanedData, cleanedSchema, suggestion.parameters!)
      cleanedData = result.data
      cleanedSchema = result.schema
      break

    case 'convert_type':
      cleanedData = convertDataType(cleanedData, suggestion.parameters!)
      break
  }

  return { data: cleanedData, schema: cleanedSchema }
}

function fillMissingValues(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  const { method, column } = params
  const columnData = data.map(row => row[column]).filter(val => val !== null && val !== undefined && val !== '')

  let fillValue: any

  switch (method) {
    case 'mean':
      const numericValues = columnData.map(val => Number(val)).filter(val => !isNaN(val))
      fillValue = numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : 0
      break

    case 'mode':
      const counts = columnData.reduce((acc, val) => {
        acc[val] = (acc[val] || 0) + 1
        return acc
      }, {} as Record<string, number>)
      fillValue = Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b, '')
      break

    default:
      fillValue = ''
  }

  return data.map(row => ({
    ...row,
    [column]: (row[column] === null || row[column] === undefined || row[column] === '') ? fillValue : row[column]
  }))
}

function removeMissingValues(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  const { column } = params
  return data.filter(row => row[column] !== null && row[column] !== undefined && row[column] !== '')
}

function interpolateValues(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  const { column } = params
  const result = [...data]

  for (let i = 0; i < result.length; i++) {
    if (result[i][column] === null || result[i][column] === undefined || result[i][column] === '') {
      // Find previous and next valid values
      let prevValue: number | null = null
      let nextValue: number | null = null
      let prevIndex = -1
      let nextIndex = -1

      // Look backwards
      for (let j = i - 1; j >= 0; j--) {
        const val = Number(result[j][column])
        if (!isNaN(val) && isFinite(val)) {
          prevValue = val
          prevIndex = j
          break
        }
      }

      // Look forwards
      for (let j = i + 1; j < result.length; j++) {
        const val = Number(result[j][column])
        if (!isNaN(val) && isFinite(val)) {
          nextValue = val
          nextIndex = j
          break
        }
      }

      // Interpolate
      if (prevValue !== null && nextValue !== null) {
        const ratio = (i - prevIndex) / (nextIndex - prevIndex)
        result[i][column] = prevValue + (nextValue - prevValue) * ratio
      } else if (prevValue !== null) {
        result[i][column] = prevValue
      } else if (nextValue !== null) {
        result[i][column] = nextValue
      }
    }
  }

  return result
}

function standardizeFormat(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  // Implementation would depend on specific format standardization needs
  return data
}

function removeDuplicates(data: Record<string, any>[]): Record<string, any>[] {
  const seen = new Set<string>()
  return data.filter(row => {
    const rowString = JSON.stringify(row)
    if (seen.has(rowString)) {
      return false
    }
    seen.add(rowString)
    return true
  })
}

function handleOutliers(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  const { method, column, lowerBound, upperBound } = params

  if (method === 'remove') {
    return data.filter(row => {
      const value = Number(row[column])
      return isNaN(value) || (value >= lowerBound && value <= upperBound)
    })
  } else if (method === 'cap') {
    return data.map(row => {
      const value = Number(row[column])
      if (isNaN(value)) return row

      let cappedValue = value
      if (value < lowerBound) cappedValue = lowerBound
      if (value > upperBound) cappedValue = upperBound

      return { ...row, [column]: cappedValue }
    })
  }

  return data
}

function renameColumn(
  data: Record<string, any>[],
  schema: Record<string, string>,
  params: Record<string, any>
): { data: Record<string, any>[], schema: Record<string, string> } {
  const { oldName, newName } = params

  const newData = data.map(row => {
    const newRow = { ...row }
    if (oldName in newRow) {
      newRow[newName] = newRow[oldName]
      delete newRow[oldName]
    }
    return newRow
  })

  const newSchema = { ...schema }
  if (oldName in newSchema) {
    newSchema[newName] = newSchema[oldName]
    delete newSchema[oldName]
  }

  return { data: newData, schema: newSchema }
}

function convertDataType(data: Record<string, any>[], params: Record<string, any>): Record<string, any>[] {
  const { targetType, column } = params

  return data.map(row => {
    let value = row[column]

    if (targetType === 'number' && value !== null && value !== undefined && value !== '') {
      // Try to convert to number
      const numericValue = Number(String(value).replace(/[^0-9.-]/g, ''))
      if (!isNaN(numericValue) && isFinite(numericValue)) {
        value = numericValue
      }
    }

    return { ...row, [column]: value }
  })
}
