'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Info,
  Loader2,
  ArrowRight,
  ArrowLeft,
  Eye,
  EyeOff,
  Sparkles,
  Database,
  TrendingUp,
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { analyzeDataQuality, applyDataCleaningSuggestion, DataQualityReport, DataCleaningSuggestion } from '@/lib/data-analysis'
import { googleSheetsService } from '@/lib/google-sheets'
import { dataSourcesService } from '@/lib/firestore'
import { DataSource as FirebaseDataSource } from '@/lib/database.types'

interface DataCleaningStepProps {
  dataSource: {
    id: string
    name: string
    type: 'csv' | 'google_sheets' | 'api'
  }
  onContinue: (cleanedData: Record<string, any>[], cleanedSchema: Record<string, string>) => void
  onBack: () => void
}

export function DataCleaningStep({ dataSource, onContinue, onBack }: DataCleaningStepProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(true)
  const [analysisError, setAnalysisError] = useState<string>('')
  const [qualityReport, setQualityReport] = useState<DataQualityReport | null>(null)
  const [originalData, setOriginalData] = useState<Record<string, any>[]>([])
  const [originalSchema, setOriginalSchema] = useState<Record<string, string>>({})
  const [cleanedData, setCleanedData] = useState<Record<string, any>[]>([])
  const [cleanedSchema, setCleanedSchema] = useState<Record<string, string>>({})
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set())
  const [showPreview, setShowPreview] = useState(false)
  const [previewSuggestion, setPreviewSuggestion] = useState<DataCleaningSuggestion | null>(null)

  useEffect(() => {
    analyzeDataSource()
  }, [dataSource])

  const analyzeDataSource = async () => {
    try {
      setIsAnalyzing(true)
      setAnalysisError('')

      // First, get the data source configuration from Firebase
      const firebaseDataSource = await dataSourcesService.getById(dataSource.id) as FirebaseDataSource
      if (!firebaseDataSource) {
        throw new Error('Data source not found')
      }

      // Fetch data from the data source based on its type and configuration
      let data: Record<string, any>[] = []
      let schema: Record<string, string> = {}

      if (dataSource.type === 'google_sheets') {
        const config = firebaseDataSource.config as any
        const sheetData = await googleSheetsService.getSheetData(
          config.sheet_id,
          config.sheet_tab || config.sheet_name || 'Sheet1'
        )
        data = sheetData.data
        schema = sheetData.schema
      }
      // Add other data source types as needed (CSV, API, etc.)

      if (data.length === 0) {
        throw new Error('No data found in the selected data source')
      }

      setOriginalData(data)
      setOriginalSchema(schema)
      setCleanedData(data)
      setCleanedSchema(schema)

      // Analyze data quality
      const report = analyzeDataQuality(data, schema)
      setQualityReport(report)

    } catch (error) {
      console.error('Error analyzing data source:', error)
      setAnalysisError(error instanceof Error ? error.message : 'Failed to analyze data source')
    } finally {
      setIsAnalyzing(false)
    }
  }

  const applySuggestion = (suggestion: DataCleaningSuggestion) => {
    try {
      const result = applyDataCleaningSuggestion(cleanedData, cleanedSchema, suggestion)
      setCleanedData(result.data)
      setCleanedSchema(result.schema)
      setAppliedSuggestions(prev => new Set([...prev, suggestion.id]))
    } catch (error) {
      console.error('Error applying suggestion:', error)
    }
  }

  const revertSuggestion = (suggestion: DataCleaningSuggestion) => {
    // For simplicity, we'll re-apply all suggestions except the one being reverted
    let data = [...originalData]
    let schema = { ...originalSchema }
    const newAppliedSuggestions = new Set(appliedSuggestions)
    newAppliedSuggestions.delete(suggestion.id)

    if (qualityReport) {
      qualityReport.suggestions
        .filter(s => newAppliedSuggestions.has(s.id))
        .forEach(s => {
          const result = applyDataCleaningSuggestion(data, schema, s)
          data = result.data
          schema = result.schema
        })
    }

    setCleanedData(data)
    setCleanedSchema(schema)
    setAppliedSuggestions(newAppliedSuggestions)
  }

  const previewSuggestionEffect = (suggestion: DataCleaningSuggestion) => {
    const result = applyDataCleaningSuggestion(cleanedData, cleanedSchema, suggestion)
    setPreviewSuggestion({
      ...suggestion,
      preview: {
        before: cleanedData.slice(0, 5),
        after: result.data.slice(0, 5)
      }
    })
    setShowPreview(true)
  }

  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return <XCircle className="h-4 w-4 text-red-500" />
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'low': return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getSeverityColor = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (isAnalyzing) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <Loader2 className="h-10 w-10 text-white animate-spin" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Analyzing Data Quality</h2>
            <p className="text-gray-600 mb-8">
              We're examining your data for quality issues and preparing AI-powered suggestions...
            </p>
            <div className="w-64 h-2 bg-gray-200 rounded-full mx-auto">
              <div className="h-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full animate-pulse" style={{width: '60%'}}></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (analysisError) {
    return (
      <div className="space-y-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-red-100 rounded-2xl mx-auto mb-6 flex items-center justify-center">
              <AlertCircle className="h-10 w-10 text-red-500" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">Analysis Failed</h2>
            <p className="text-red-600 mb-8">{analysisError}</p>
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={onBack} className="rounded-xl">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Data Selection
              </Button>
              <Button onClick={analyzeDataSource} className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!qualityReport) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Data Quality Analysis</h2>
            <p className="text-sm text-gray-600">Review and apply AI-powered suggestions to improve your data quality</p>
          </div>
          <div className="flex items-center space-x-2">
            <Database className="h-5 w-5 text-gray-400" />
            <span className="text-sm text-gray-600">{dataSource.name}</span>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 rounded-xl p-4">
            <div className="text-2xl font-bold text-gray-900">{qualityReport.summary.totalIssues}</div>
            <div className="text-sm text-gray-600">Total Issues</div>
          </div>
          <div className="bg-red-50 rounded-xl p-4">
            <div className="text-2xl font-bold text-red-600">{qualityReport.summary.highSeverityIssues}</div>
            <div className="text-sm text-gray-600">High Priority</div>
          </div>
          <div className="bg-yellow-50 rounded-xl p-4">
            <div className="text-2xl font-bold text-yellow-600">{qualityReport.summary.mediumSeverityIssues}</div>
            <div className="text-sm text-gray-600">Medium Priority</div>
          </div>
          <div className="bg-green-50 rounded-xl p-4">
            <div className="text-2xl font-bold text-green-600">{originalData.length - qualityReport.summary.affectedRows}</div>
            <div className="text-sm text-gray-600">Clean Rows</div>
          </div>
        </div>

        {qualityReport.summary.totalIssues === 0 && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              Great! Your data appears to be clean and ready for analysis. No quality issues were detected.
            </AlertDescription>
          </Alert>
        )}
      </div>

      {/* Issues and Suggestions */}
      {qualityReport.summary.totalIssues > 0 && (
        <div className="space-y-4">
          {qualityReport.issues.map((issue) => {
            const relatedSuggestions = qualityReport.suggestions.filter(s => s.issueId === issue.id)

            return (
              <Card key={issue.id} className="border border-gray-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getSeverityIcon(issue.severity)}
                      <div>
                        <CardTitle className="text-lg">{issue.description}</CardTitle>
                        <CardDescription className="mt-1">
                          {issue.column && `Column: ${issue.column} • `}
                          Affects {issue.affectedRows} rows
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className={`${getSeverityColor(issue.severity)} border`}>
                      {issue.severity.toUpperCase()}
                    </Badge>
                  </div>
                </CardHeader>

                {relatedSuggestions.length > 0 && (
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Sparkles className="h-4 w-4" />
                        <span>AI-Powered Suggestions</span>
                      </div>

                      <div className="space-y-2">
                        {relatedSuggestions.map((suggestion) => {
                          const isApplied = appliedSuggestions.has(suggestion.id)

                          return (
                            <div key={suggestion.id} className={`p-4 rounded-lg border transition-all ${
                              isApplied ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200 hover:border-gray-300'
                            }`}>
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <h4 className="font-medium text-gray-900">{suggestion.title}</h4>
                                    <span className={`text-xs font-medium ${getConfidenceColor(suggestion.confidence)}`}>
                                      {Math.round(suggestion.confidence * 100)}% confidence
                                    </span>
                                    {isApplied && <CheckCircle2 className="h-4 w-4 text-green-600" />}
                                  </div>
                                  <p className="text-sm text-gray-600 mb-2">{suggestion.description}</p>
                                  <p className="text-xs text-gray-500">{suggestion.explanation}</p>
                                </div>

                                <div className="flex items-center space-x-2 ml-4">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => previewSuggestionEffect(suggestion)}
                                    className="text-xs"
                                  >
                                    <Eye className="h-3 w-3 mr-1" />
                                    Preview
                                  </Button>

                                  {isApplied ? (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => revertSuggestion(suggestion)}
                                      className="text-xs text-red-600 hover:text-red-700"
                                    >
                                      Revert
                                    </Button>
                                  ) : (
                                    <Button
                                      size="sm"
                                      onClick={() => applySuggestion(suggestion)}
                                      className="text-xs bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                                    >
                                      Apply
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            )
          })}
        </div>
      )}

      {/* Data Preview Modal */}
      {showPreview && previewSuggestion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{previewSuggestion.title}</h3>
                  <p className="text-sm text-gray-600">{previewSuggestion.description}</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(false)}
                  className="rounded-xl"
                >
                  <EyeOff className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="p-6 overflow-y-auto max-h-[60vh]">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Before</h4>
                  <div className="bg-red-50 rounded-lg p-4 overflow-x-auto">
                    <table className="w-full text-xs">
                      <thead>
                        <tr className="border-b border-red-200">
                          {Object.keys(previewSuggestion.preview?.before[0] || {}).map(key => (
                            <th key={key} className="text-left p-1 font-medium text-red-800">{key}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {previewSuggestion.preview?.before.map((row, idx) => (
                          <tr key={idx} className="border-b border-red-100">
                            {Object.values(row).map((value, cellIdx) => (
                              <td key={cellIdx} className="p-1 text-red-700">
                                {value === null || value === undefined || value === '' ? (
                                  <span className="text-red-400 italic">empty</span>
                                ) : (
                                  String(value)
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">After</h4>
                  <div className="bg-green-50 rounded-lg p-4 overflow-x-auto">
                    <table className="w-full text-xs">
                      <thead>
                        <tr className="border-b border-green-200">
                          {Object.keys(previewSuggestion.preview?.after[0] || {}).map(key => (
                            <th key={key} className="text-left p-1 font-medium text-green-800">{key}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {previewSuggestion.preview?.after.map((row, idx) => (
                          <tr key={idx} className="border-b border-green-100">
                            {Object.values(row).map((value, cellIdx) => (
                              <td key={cellIdx} className="p-1 text-green-700">
                                {value === null || value === undefined || value === '' ? (
                                  <span className="text-green-400 italic">empty</span>
                                ) : (
                                  String(value)
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={onBack}
            className="rounded-xl"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Data Selection
          </Button>

          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              {appliedSuggestions.size > 0 && (
                <span className="flex items-center space-x-1">
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                  <span>{appliedSuggestions.size} improvements applied</span>
                </span>
              )}
            </div>

            <Button
              onClick={() => onContinue(cleanedData, cleanedSchema)}
              className="rounded-xl bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              Continue to Report Generation
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
